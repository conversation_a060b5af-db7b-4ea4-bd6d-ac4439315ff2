<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Export Businesses Using Overpass (No Google API)</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 2rem;
    }
    /* Navigation styles */
    .main-nav {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1rem;
    }
    .main-nav a {
      color: #004B8D;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem;
    }
    .main-nav a:hover {
      text-decoration: underline;
    }
    .categories-container {
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 10px;
      margin-bottom: 20px;
    }
    .categories-header {
      font-weight: bold;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
    .key-groups-container {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }
    .key-group {
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      padding: 8px;
      background-color: #f9f9f9;
      flex: 1 0 300px;
      max-width: 400px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .key-header {
      font-weight: bold;
      margin-bottom: 8px;
      padding-bottom: 4px;
      border-bottom: 1px solid #eee;
      color: #555;
      position: relative;
    }

    .key-header .group-delete-btn {
      display: none;
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      color: #d32f2f;
      font-weight: bold;
      cursor: pointer;
      font-size: 1.1rem;
      line-height: 1;
      padding: 2px 4px;
      border-radius: 3px;
      background: rgba(255, 255, 255, 0.9);
    }

    .key-header .group-delete-btn:hover {
      background: #d32f2f;
      color: white;
    }

    .key-header:hover .group-delete-btn {
      display: block;
    }
    .key-items {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      max-height: 300px;
      overflow-y: auto;
    }
    .key-items.columns-1 {
      column-count: 1;
    }
    .key-items.columns-2 {
      column-count: 2;
    }
    .key-items.columns-3 {
      column-count: 3;
    }
    .key-items.scrollable {
      max-height: 300px;
      overflow-y: auto;
    }

    .actions {
      margin: 20px 0;
    }
    button {
      padding: 0.5rem 1rem;
      font-size: 1rem;
      cursor: pointer;
      margin-right: 0.5rem;
    }
    #log {
      margin-top: 1rem;
      white-space: pre-wrap;
      background: #f5f5f5;
      padding: 1rem;
      border-radius: 4px;
      height: 300px;
      overflow-y: auto;
      font-family: Consolas, monospace;
      font-size: 0.9rem;
    }
    .download-link {
      margin: 0.5rem 0;
      display: block;
    }
    
    /* Key Header and Inline Add Styles */
    .key-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .key-header .add-new {
      cursor: pointer;
      font-size: 1.2rem;
      color: #10a37f;
      margin-left: 8px;
    }
    .key-header .add-new:hover {
      color: #0e8c65;
    }
    .category-item {
      position: relative;
      padding: 3px;
      font-size: 0.9rem;
      break-inside: avoid;
      page-break-inside: avoid;
    }
    .category-item label {
      display: flex;
      align-items: flex-start;
    }
    .category-item input[type="checkbox"] {
      margin-right: 5px;
      margin-top: 3px;
    }
    .category-item .remove-btn {
      display: none;
      position: absolute;
      right: 4px;
      top: 4px;
      color: #d32f2f;
      font-weight: bold;
      cursor: pointer;
      font-size: 1rem;
      line-height: 1;
    }
    .category-item:hover .remove-btn {
      display: block;
    }
    .category-item.new-entry {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 5px;
    }
    .category-item.new-entry input[type="text"] {
      flex: 1;
      padding: 4px 6px;
      font-size: 0.9rem;
      border: 1px solid #ccc;
      border-radius: 3px;
    }
    .category-item.new-entry .cancel-new {
      color: #d32f2f;
      cursor: pointer;
      font-size: 1.1rem;
      line-height: 1;
    }
    .category-item.new-entry .ask-chatgpt-link {
      font-size: 0.9rem;
      text-decoration: none;
      color: #004B8D;
      margin-left: 6px;
    }
    .category-item.new-entry .ask-chatgpt-link:hover {
      text-decoration: underline;
    }

    /* Header Add Group Styles */
    .categories-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .categories-header .add-group {
      cursor: pointer;
      font-size: 1.2rem;
      color: #10a37f;
      margin-left: 8px;
    }
    .categories-header .add-group:hover {
      color: #0e8c65;
    }
    .header-new-entry {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
    }
    .header-new-entry input[type="text"] {
      flex: 1;
      padding: 4px 6px;
      font-size: 0.9rem;
      border: 1px solid #ccc;
      border-radius: 3px;
    }
    .header-new-entry .cancel-new {
      color: #d32f2f;
      cursor: pointer;
      font-size: 1.1rem;
      line-height: 1;
    }
    .header-new-entry .ask-chatgpt-link {
      font-size: 0.9rem;
      text-decoration: none;
      color: #004B8D;
    }
    .header-new-entry .ask-chatgpt-link:hover {
      text-decoration: underline;
    }

    /* STORY OF PAGE POPUP STYLES */
    .hidden-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.6);
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    .visible-overlay {
      display: flex;
    }
    .story-dialog {
      background: #fff;
      border-radius: 8px;
      max-width: 600px;
      width: 90%;
      padding: 1.5rem;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      position: relative;
    }
    .story-header h2 {
      margin: 0 0 1rem 0;
      color: #004B8D; /* myTech.Today branded color */
      font-size: 1.5rem;
    }
    .story-content {
      max-height: 70vh;
      overflow-y: auto;
      font-family: Arial, sans-serif;
      font-size: 0.95rem;
      line-height: 1.4;
    }
    .story-content ul {
      padding-left: 1.5rem;
    }
    .story-content li {
      margin-bottom: 0.75rem;
    }
    .signature-block {
      margin-top: 1.5rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
      font-weight: 500;
    }
    .signature-block a {
      display: block;
      margin-top: 0.25rem;
      color: #004B8D;
      text-decoration: none;
    }
    .signature-block a:hover {
      text-decoration: underline;
    }
    .close-btn {
      position: absolute;
      top: 8px;
      right: 8px;
      background: transparent;
      border: none;
      font-size: 1.5rem;
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #555;
      cursor: pointer;
    }
    .close-btn:hover {
      color: #000;
    }

    /* INSTRUCTIONS POPUP STYLES */
    .instructions-dialog {
      background: #fff;
      border-radius: 8px;
      max-width: 90vw;
      width: 1200px;
      height: 80vh;
      padding: 1.5rem;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      position: relative;
      display: flex;
      flex-direction: column;
    }
    .instructions-header h2 {
      margin: 0 0 1rem 0;
      color: #004B8D; /* myTech.Today branded color */
      font-size: 1.5rem;
    }
    .instructions-content {
      flex: 1;
      overflow: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fff;
    }
    .markdown-content {
      padding: 2rem;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #24292f;
      max-width: none;
    }
    .markdown-content h1 {
      font-size: 2rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
      padding-bottom: 0.3rem;
      border-bottom: 1px solid #d1d9e0;
      color: #004B8D;
    }
    .markdown-content h2 {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 2rem 0 1rem 0;
      padding-bottom: 0.3rem;
      border-bottom: 1px solid #d1d9e0;
      color: #004B8D;
    }
    .markdown-content h3 {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 1.5rem 0 0.5rem 0;
      color: #004B8D;
    }
    .markdown-content h4 {
      font-size: 1rem;
      font-weight: 600;
      margin: 1rem 0 0.5rem 0;
      color: #004B8D;
    }
    .markdown-content p {
      margin: 0 0 1rem 0;
    }
    .markdown-content ul, .markdown-content ol {
      margin: 0 0 1rem 0;
      padding-left: 2rem;
    }
    .markdown-content li {
      margin: 0.25rem 0;
    }
    .markdown-content code {
      background: #f6f8fa;
      padding: 0.2rem 0.4rem;
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 0.85rem;
    }
    .markdown-content pre {
      background: #f6f8fa;
      padding: 1rem;
      border-radius: 6px;
      overflow-x: auto;
      margin: 1rem 0;
    }
    .markdown-content pre code {
      background: none;
      padding: 0;
    }
    .markdown-content blockquote {
      border-left: 4px solid #d1d9e0;
      padding: 0 1rem;
      margin: 1rem 0;
      color: #656d76;
    }
    .markdown-content a {
      color: #0969da;
      text-decoration: none;
    }
    .markdown-content a:hover {
      text-decoration: underline;
    }
    .markdown-content strong {
      font-weight: 600;
    }
    .markdown-content em {
      font-style: italic;
    }
    .markdown-content hr {
      border: none;
      border-top: 1px solid #d1d9e0;
      margin: 2rem 0;
    }
    .loading-message {
      text-align: center;
      padding: 2rem;
      color: #656d76;
      font-style: italic;
    }
    .error-message {
      text-align: center;
      padding: 2rem;
      color: #d1242f;
      background: #fff8f8;
      border: 1px solid #f8d7da;
      border-radius: 4px;
      margin: 1rem;
    }

    /* ZIP code and coordinates styling */
    #coordsDisplay {
      font-family: Consolas, monospace;
      font-weight: 500;
      color: #004B8D;
    }

    .coords-info {
      color: #666;
    }

    .status-message {
      font-size: 0.9rem;
      margin-top: 5px;
      font-style: italic;
    }

    #radiusMiles {
      width: 60px;
    }

    #zipCode {
      width: 70px;
    }

    .chatgpt-link {
      margin-left: 10px;
    }

    #resetCategoriesLink {
      margin-left: 10px;
      color: #d32f2f;
      font-weight: 500;
    }

    #restoreGroupsLink {
      margin-left: 10px;
      color: #1976d2;
      font-weight: 500;
    }
  </style>
  <!-- Add JSZip library in the head section -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
</head>
<body>
  <!-- STORY OF PAGE POPUP -->
  <div id="pageStoryOverlay" class="hidden-overlay">
    <div id="pageStoryDialog" class="story-dialog" tabindex="-1">
      <button id="closePageStory" class="close-btn" aria-label="Close">&times;</button>
      <header class="story-header">
        <h2>The Story of This Page</h2>
      </header>
      <div class="story-content">
        <ul>
          <li>This page was originally described as a <a href="https://chatgpt.com/share/683fae9c-f324-8013-b4ba-b9b978770b26" target="_blank">ChatGPT prompt</a>.</li>
          <li>ChatGPT generated a suitable Python program to accomplish the task, but to avoid Python dependencies, the reply was refactored as an HTML page.  This allows anyone to use the page without needing to install Python.</li>
          <li>Again, to avoid Google Dependencies, the mapping engine was switched from Google Maps to OpenStreetMap.  This allows for anyone to use the page without needing a Google API key.</li>
          <li>The resulting code was copy-and-pasted into VS Code and opened with <a href="https://www.augmentcode.com/" target="_blank">Augment</a>.</li>
          <li>Within VS Code + Augment, this project was massaged and refactored multiple times.  Ultimately, the use of the Augment AI Agent is the best way to '<i>vibe code</i>.'</li>
          <li>The code was also pushed to GitHub: <a href="https://github.com/mytech-today-now/business_search" target="_blank">mytech-today-now/business_search</a>.</li>
          <li>A separate ChatGPT prompt produced 35 business types that benefit from MSP services in Chicagoland; the reply was reformatted as a comma-separated list and re-fed into ChatGPT/Augment.</li>
          <li>On June 1, 2025, the code was refactored 29 times using Augment (50 total prompts to generate and refine code). Additional prompts refined and improved the final version.</li>
          <li>Finally, the entire codebase was fed back into ChatGPT for further prompt generation, then re-applied in Augment. This iterative AI-driven workflow has been an extremely effective way to "vibe code."</li>
          <li>By comparison, 15 years ago I wrote this same program in C# over four weeks. The AI version took 1.5 days with almost zero manual effort.</li>
        </ul>
        <div class="signature-block">
          Kyle Rode
          <a href="https://mytech.today" target="_blank">myTech.Today</a>
          <a href="tel:***********" target="_blank">(*************</a>
          <a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>
        </div>
      </div>
    </div>
  </div>

  <!-- INSTRUCTIONS POPUP -->
  <div id="instructionsOverlay" class="hidden-overlay">
    <div id="instructionsDialog" class="instructions-dialog" tabindex="-1">
      <button id="closeInstructions" class="close-btn" aria-label="Close">&times;</button>
      <header class="instructions-header">
        <h2>Instructions & Documentation</h2>
      </header>
      <div class="instructions-content">
        <div id="instructionsContent" class="markdown-content">
          <div class="loading-message">Loading documentation...</div>
        </div>
      </div>
    </div>
  </div>

  <nav class="main-nav">
    <a href="#" id="openInstructions">Instructions</a>
    <a href="#" id="openPageStory">The story of this Page</a>
  </nav>
  
  <h1>Export Businesses (<span id="radiusDisplay">30</span> mi radius around <span id="zipDisplay">60010</span>) as CSV</h1>
  <p>
    This page uses the Overpass API (<a href="https://www.openstreetmap.org/about" target="_blank" rel="noopener noreferrer">OpenStreetMap</a>) to fetch business data in a
    <input type="number" id="radiusMiles" value="30" min="1" max="100"> mi
    (<span id="radiusMeters">48280</span>m) radius around ZIP
    <input type="text" id="zipCode" value="60010" pattern="[0-9]{5}" maxlength="5" title="Enter a 5-digit ZIP code and press Enter or click away to update coordinates">.
    <br><small class="coords-info">Current coordinates: <span id="coordsDisplay">42.1543, -88.1362</span></small>
    <br>Select categories below and click "Run Export" to generate CSVs. No API key is required. <a href="#" id="askChatGPTLink" target="_blank" rel="noopener noreferrer" class="chatgpt-link">Ask ChatGPT for OSM tags</a><a href="#" id="resetCategoriesLink">Reset Categories</a><a href="#" id="restoreGroupsLink">Restore Hidden Groups</a>
  </p>

  <div class="categories-container">
    <div class="categories-header">
      <label><input type="checkbox" id="selectAllCategories"> Select/Unselect All Categories</label>
      <span class="add-group" title="Add New Group">+</span>
    </div>
    <div id="headerNewEntryContainer"></div>
    <div class="category-items" id="allCategories"></div>
  </div>

  <div class="actions">
    <button id="runBtn">Run Export</button>
    <button id="downloadAllBtn" disabled>Download All as ZIP</button>
  </div>

  <div id="log"></div>
  <div id="downloads"></div>

  <script>
    (function () {
      // ======================================================
      // User Categories Storage Helpers
      // ======================================================
      function loadUserCategories() {
        try {
          const stored = localStorage.getItem("userCategories");
          if (!stored) return {};
          return JSON.parse(stored) || {};
        } catch (err) {
          console.error("Error loading user categories:", err);
          return {};
        }
      }
      
      function saveUserCategories(userCategories) {
        try {
          localStorage.setItem("userCategories", JSON.stringify(userCategories));
          return true;
        } catch (err) {
          console.error("Error saving user categories:", err);
          return false;
        }
      }

      // ======================================================
      // User Groups Storage Helpers
      // ======================================================
      function loadUserGroups() {
        try {
          const stored = localStorage.getItem("userGroups");
          if (!stored) return [];
          return JSON.parse(stored) || [];
        } catch (err) {
          console.error("Error loading user groups:", err);
          return [];
        }
      }

      function saveUserGroups(userGroups) {
        try {
          localStorage.setItem("userGroups", JSON.stringify(userGroups));
          return true;
        } catch (err) {
          console.error("Error saving user groups:", err);
          return false;
        }
      }
      
      // ======================================================
      // Configuration
      // ======================================================
      // Default coordinates for ZIP 60010 (Barrington, IL) - make global
      window.LAT = 42.1543;
      window.LNG = -88.1362;
      window.ZIP_CODE = "60010";

      // Default radius: 30 miles - make global
      const MILES_TO_METERS = 1609.34;
      let RADIUS_MILES = 30;
      window.RADIUS = Math.round(RADIUS_MILES * MILES_TO_METERS);

      // DOM elements for radius and ZIP
      const radiusMilesInput = document.getElementById("radiusMiles");
      const radiusMetersSpan = document.getElementById("radiusMeters");
      const radiusDisplay = document.getElementById("radiusDisplay");
      const zipCodeInput = document.getElementById("zipCode");
      const zipDisplay = document.getElementById("zipDisplay");
      const coordsDisplay = document.getElementById("coordsDisplay");
      const statusMessage = document.createElement("div");
      statusMessage.className = "status-message";
      zipCodeInput.parentNode.appendChild(statusMessage);

      // Update radius in meters when miles input changes
      radiusMilesInput.addEventListener("input", function() {
        RADIUS_MILES = parseFloat(this.value) || 30;
        window.RADIUS = Math.round(RADIUS_MILES * MILES_TO_METERS);
        radiusMetersSpan.textContent = window.RADIUS;
        radiusDisplay.textContent = RADIUS_MILES;
      });

      // Geocode ZIP code using Census Geocoding API - make it globally accessible
      window.geocodeZipCode = async function geocodeZipCode(zipCode) {
        statusMessage.textContent = `Looking up coordinates for ${zipCode}...`;
        statusMessage.style.color = "#666";

        try {
          // Try using Census Geocoding API with no-cors mode
          try {
            const response = await fetch(`https://geocoding.geo.census.gov/geocoder/locations/address?benchmark=2020&format=json&zip=${zipCode}`, {
              mode: 'no-cors',
              headers: {
                'Accept': 'application/json'
              }
            });

            // Note: With no-cors, we can't actually read the response content
            // So we'll immediately fall back to our local database
            throw new Error("Using no-cors mode, falling back to local database");

          } catch (corsError) {
            console.warn("CORS issue with Census API, using fallback method");
            // Continue to fallback method
          }
          
          // Fallback: Use a hardcoded mapping of common ZIP codes
          const zipCoordinates = {
            // North Suburbs
            "60002": { lat: 42.4639, lng: -87.9957 }, // Antioch, IL
            "60004": { lat: 42.0828, lng: -87.9803 }, // Arlington Heights, IL
            "60005": { lat: 42.0654, lng: -87.9806 }, // Arlington Heights, IL
            "60007": { lat: 42.0372, lng: -87.9925 }, // Elk Grove Village, IL
            "60008": { lat: 42.0503, lng: -88.0198 }, // Rolling Meadows, IL
            "60010": { lat: 42.1543, lng: -88.1362 }, // Barrington, IL
            "60012": { lat: 42.2297, lng: -88.3031 }, // Crystal Lake, IL
            "60013": { lat: 42.2389, lng: -88.3173 }, // Cary, IL
            "60014": { lat: 42.2411, lng: -88.3162 }, // Crystal Lake, IL
            "60015": { lat: 42.1711, lng: -87.7890 }, // Deerfield, IL
            "60016": { lat: 42.0411, lng: -87.8878 }, // Des Plaines, IL
            "60018": { lat: 42.0064, lng: -87.9339 }, // Des Plaines, IL
            "60020": { lat: 42.2364, lng: -88.1962 }, // Fox Lake, IL
            "60021": { lat: 42.1917, lng: -88.2356 }, // Fox River Grove, IL
            "60022": { lat: 42.1464, lng: -87.7859 }, // Glencoe, IL
            "60025": { lat: 42.0697, lng: -87.8270 }, // Glenview, IL
            "60026": { lat: 42.0697, lng: -87.8270 }, // Glenview, IL
            "60029": { lat: 42.0411, lng: -87.8878 }, // Golf, IL
            "60030": { lat: 42.3828, lng: -87.9581 }, // Grayslake, IL
            "60031": { lat: 42.3756, lng: -87.9339 }, // Gurnee, IL
            "60035": { lat: 42.1856, lng: -87.7996 }, // Highland Park, IL
            "60040": { lat: 42.2078, lng: -87.8320 }, // Highwood, IL
            "60041": { lat: 42.4142, lng: -88.0931 }, // Ingleside, IL
            "60042": { lat: 42.4500, lng: -88.0964 }, // Island Lake, IL
            "60043": { lat: 42.1439, lng: -87.7645 }, // Kenilworth, IL
            "60044": { lat: 42.2625, lng: -87.8414 }, // Lake Bluff, IL
            "60045": { lat: 42.2336, lng: -87.8481 }, // Lake Forest, IL
            "60046": { lat: 42.4139, lng: -87.8831 }, // Lake Villa, IL
            "60047": { lat: 42.1950, lng: -88.0797 }, // Lake Zurich, IL
            "60048": { lat: 42.2836, lng: -87.9400 }, // Libertyville, IL
            "60050": { lat: 42.3331, lng: -88.2728 }, // McHenry, IL
            "60051": { lat: 42.3500, lng: -88.2667 }, // McHenry, IL
            "60053": { lat: 42.0664, lng: -87.7479 }, // Morton Grove, IL
            "60056": { lat: 42.0806, lng: -87.9367 }, // Mount Prospect, IL
            "60060": { lat: 42.3142, lng: -87.9464 }, // Mundelein, IL
            "60061": { lat: 42.2178, lng: -87.9125 }, // Vernon Hills, IL
            "60062": { lat: 42.1467, lng: -87.7925 }, // Northbrook, IL
            "60064": { lat: 42.3000, lng: -87.8333 }, // North Chicago, IL
            "60067": { lat: 42.1103, lng: -88.0539 }, // Palatine, IL
            "60068": { lat: 42.0111, lng: -87.8406 }, // Park Ridge, IL
            "60069": { lat: 42.1825, lng: -88.0017 }, // Lincolnshire, IL
            "60070": { lat: 42.1489, lng: -88.0831 }, // Prospect Heights, IL
            "60073": { lat: 42.4500, lng: -88.0000 }, // Round Lake, IL
            "60074": { lat: 42.0483, lng: -88.0817 }, // Palatine, IL
            "60076": { lat: 42.0397, lng: -87.7337 }, // Skokie, IL
            "60077": { lat: 42.0230, lng: -87.7581 }, // Skokie, IL
            "60081": { lat: 42.4167, lng: -88.1333 }, // Spring Grove, IL
            "60082": { lat: 42.1272, lng: -87.8625 }, // Techny, IL
            "60083": { lat: 42.4500, lng: -87.9833 }, // Wadsworth, IL
            "60084": { lat: 42.3333, lng: -88.0000 }, // Wauconda, IL
            "60085": { lat: 42.3639, lng: -87.8447 }, // Waukegan, IL
            "60087": { lat: 42.3639, lng: -87.8447 }, // Waukegan, IL
            "60089": { lat: 42.1492, lng: -87.9264 }, // Buffalo Grove, IL
            "60090": { lat: 42.1417, lng: -87.9278 }, // Wheeling, IL
            "60091": { lat: 42.0780, lng: -87.7106 }, // Wilmette, IL
            "60093": { lat: 42.1083, lng: -87.7340 }, // Winnetka, IL
            "60096": { lat: 42.1967, lng: -87.9472 }, // Wauconda, IL
            "60099": { lat: 42.4464, lng: -87.8031 }, // Zion, IL

            // Chicago City Proper
            "60601": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60602": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60603": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60604": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60605": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60606": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60607": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60608": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport/Chinatown)
            "60609": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport)
            "60610": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near North Side)
            "60611": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Magnificent Mile/Gold Coast)
            "60612": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60613": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lakeview)
            "60614": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lincoln Park)
            "60615": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Kenwood/Hyde Park)
            "60616": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near South Side)
            "60617": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore)
            "60618": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (North Center/Irving Park)
            "60619": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Chatham)
            "60620": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Greater Grand Crossing)
            "60621": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Fuller Park)
            "60622": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Wicker Park/Bucktown)
            "60623": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Little Village)
            "60624": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (North Lawndale)
            "60625": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Albany Park)
            "60626": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Rogers Park)
            "60628": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Roseland)
            "60629": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Gage Park)
            "60630": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Jefferson Park)
            "60631": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (O'Hare)
            "60632": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (McKinley Park)
            "60633": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Hegewisch)
            "60634": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Belmont Cragin)
            "60636": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (New City)
            "60637": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Woodlawn)
            "60638": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Ashburn)
            "60639": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Hermosa)
            "60640": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Uptown)
            "60641": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Portage Park)
            "60642": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60643": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Morgan Park)
            "60644": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Austin)
            "60645": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Ridge)
            "60646": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Norwood Park)
            "60647": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Logan Square)
            "60649": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore)
            "60651": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Garfield Park)
            "60652": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Elsdon)
            "60653": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Oakland)
            "60654": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (River North)
            "60655": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Mount Greenwood)
            "60656": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (O'Hare)
            "60657": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lakeview)
            "60659": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Ridge)
            "60660": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Edgewater)
            "60661": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (West Loop)
            "60664": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near North Side)
            "60666": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (O'Hare Airport)
            "60668": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60669": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near West Side)
            "60670": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60673": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60674": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60675": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60677": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60678": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60680": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60681": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60682": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60684": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60685": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60686": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60687": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60688": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60689": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60690": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60691": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60693": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60694": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60695": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60696": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60697": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
            "60699": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)

            // Additional Chicago South Side and Neighborhoods
            "60827": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Calumet Heights)
            "60628": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Roseland/Pullman)
            "60617": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore/Rainbow Beach)
            "60649": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Shore)
            "60619": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Chatham/Avalon Park)
            "60620": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Greater Grand Crossing)
            "60637": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Woodlawn/University of Chicago)
            "60615": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Kenwood/Hyde Park)
            "60653": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Oakland/Douglas)
            "60621": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Fuller Park/Armour Square)
            "60609": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport/McKinley Park)
            "60608": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Bridgeport/Chinatown)
            "60616": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Near South Side/South Loop)
            "60605": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (South Loop/Museum Campus)

            // West Suburbs
            "60101": { lat: 41.9372, lng: -88.0942 }, // Addison, IL
            "60102": { lat: 42.1656, lng: -88.2831 }, // Algonquin, IL
            "60103": { lat: 42.0500, lng: -88.2833 }, // Bartlett, IL
            "60106": { lat: 41.9597, lng: -88.0100 }, // Bensenville, IL
            "60107": { lat: 42.0372, lng: -88.2806 }, // Streamwood, IL
            "60108": { lat: 41.9372, lng: -88.1342 }, // Bloomingdale, IL
            "60109": { lat: 42.0667, lng: -88.8167 }, // Burlington, IL
            "60110": { lat: 42.0978, lng: -88.2789 }, // Carpentersville, IL
            "60115": { lat: 41.9294, lng: -88.7500 }, // DeKalb, IL
            "60118": { lat: 42.0950, lng: -88.2833 }, // East Dundee, IL
            "60119": { lat: 41.8900, lng: -88.4700 }, // Elburn, IL
            "60120": { lat: 42.0372, lng: -88.2806 }, // Elgin, IL
            "60123": { lat: 42.0372, lng: -88.2806 }, // Elgin, IL
            "60124": { lat: 42.0939, lng: -88.2892 }, // Elgin, IL
            "60126": { lat: 41.8994, lng: -87.9403 }, // Elmhurst, IL
            "60133": { lat: 41.9950, lng: -88.1342 }, // Hanover Park, IL
            "60134": { lat: 41.8875, lng: -88.3053 }, // Geneva, IL
            "60136": { lat: 42.1000, lng: -88.3333 }, // Gilberts, IL
            "60139": { lat: 41.9114, lng: -88.0789 }, // Glendale Heights, IL
            "60140": { lat: 42.0833, lng: -88.5333 }, // Hampshire, IL
            "60142": { lat: 42.1667, lng: -88.4500 }, // Huntley, IL
            "60143": { lat: 41.9564, lng: -88.0853 }, // Itasca, IL
            "60152": { lat: 42.2333, lng: -88.4000 }, // Marengo, IL
            "60153": { lat: 41.8764, lng: -87.8631 }, // Maywood, IL
            "60154": { lat: 41.8828, lng: -87.9589 }, // Westchester, IL
            "60156": { lat: 42.2500, lng: -88.3833 }, // Lake in the Hills, IL
            "60157": { lat: 42.0372, lng: -88.1342 }, // Medinah, IL
            "60172": { lat: 42.0089, lng: -88.0797 }, // Roselle, IL
            "60173": { lat: 42.0456, lng: -88.1152 }, // Schaumburg, IL
            "60174": { lat: 41.9142, lng: -88.3087 }, // St. Charles, IL
            "60175": { lat: 41.9142, lng: -88.3087 }, // St. Charles, IL
            "60177": { lat: 42.0333, lng: -88.3333 }, // South Elgin, IL
            "60178": { lat: 42.0500, lng: -88.7167 }, // Sycamore, IL
            "60181": { lat: 41.8700, lng: -87.9650 }, // Villa Park, IL
            "60184": { lat: 41.8517, lng: -88.2075 }, // Wayne, IL
            "60185": { lat: 41.8528, lng: -88.2042 }, // West Chicago, IL
            "60187": { lat: 41.8656, lng: -88.1069 }, // Wheaton, IL
            "60188": { lat: 41.9322, lng: -88.1536 }, // Carol Stream, IL
            "60189": { lat: 41.7950, lng: -88.0700 }, // Wheaton, IL
            "60190": { lat: 41.8244, lng: -88.1561 }, // Winfield, IL
            "60191": { lat: 41.9578, lng: -87.9981 }, // Wood Dale, IL
            "60192": { lat: 42.0303, lng: -88.2073 }, // Hoffman Estates, IL
            "60193": { lat: 42.0303, lng: -88.2073 }, // Schaumburg, IL
            "60194": { lat: 42.0456, lng: -88.1152 }, // Schaumburg, IL
            "60195": { lat: 42.0456, lng: -88.1152 }, // Schaumburg, IL
            
            // Evanston/Skokie
            "60201": { lat: 42.0464, lng: -87.6931 }, // Evanston, IL
            "60202": { lat: 42.0464, lng: -87.6931 }, // Evanston, IL
            "60203": { lat: 42.0464, lng: -87.6931 }, // Evanston, IL
            
            // Oak Park/River Forest
            "60301": { lat: 41.8856, lng: -87.7845 }, // Oak Park, IL
            "60302": { lat: 41.8856, lng: -87.7845 }, // Oak Park, IL
            "60304": { lat: 41.8856, lng: -87.7845 }, // Oak Park, IL
            "60305": { lat: 41.8953, lng: -87.8339 }, // River Forest, IL
            "60521": { lat: 41.7992, lng: -87.9403 }, // Hinsdale, IL
            "60523": { lat: 41.8200, lng: -87.9650 }, // Oak Brook, IL
            "60525": { lat: 41.8056, lng: -87.8631 }, // La Grange, IL
            "60526": { lat: 41.8200, lng: -87.8900 }, // La Grange Park, IL
            "60532": { lat: 41.8089, lng: -88.0756 }, // Lisle, IL
            "60540": { lat: 41.7508, lng: -88.1535 }, // Naperville, IL
            "60563": { lat: 41.7508, lng: -88.1535 }, // Naperville, IL
            "60564": { lat: 41.7508, lng: -88.1535 }, // Naperville, IL
            "60565": { lat: 41.7508, lng: -88.1535 },  // Naperville, IL
            
            // South Bay Los Angeles
            "90245": { lat: 33.9164, lng: -118.4016 }, // El Segundo, CA
            "90254": { lat: 33.8598, lng: -118.3965 }, // Hermosa Beach, CA
            "90266": { lat: 33.8847, lng: -118.4109 }, // Manhattan Beach, CA
            "90277": { lat: 33.8236, lng: -118.3887 }, // Redondo Beach, CA
            "90278": { lat: 33.8729, lng: -118.3765 }, // Redondo Beach, CA
            "90501": { lat: 33.8358, lng: -118.3406 }, // Torrance, CA
            "90503": { lat: 33.8358, lng: -118.3406 }, // Torrance, CA
            "90505": { lat: 33.8058, lng: -118.3312 }, // Torrance, CA
            
            // Palos Verdes Peninsula
            "90274": { lat: 33.7866, lng: -118.3896 }, // Palos Verdes Estates, CA
            "90275": { lat: 33.7444, lng: -118.3870 }, // Rancho Palos Verdes, CA
            
            // San Pedro
            "90731": { lat: 33.7361, lng: -118.2922 }, // San Pedro, CA
            "90732": { lat: 33.7483, lng: -118.3120 }, // San Pedro, CA
            
            // Long Beach
            "90802": { lat: 33.7701, lng: -118.1937 }, // Long Beach, CA
            "90803": { lat: 33.7605, lng: -118.1306 }, // Long Beach, CA
            "90804": { lat: 33.7900, lng: -118.1581 }, // Long Beach, CA
            "90805": { lat: 33.8633, lng: -118.1801 }, // Long Beach, CA
            "90806": { lat: 33.8019, lng: -118.1873 }, // Long Beach, CA
            "90807": { lat: 33.8303, lng: -118.1801 }, // Long Beach, CA
            "90808": { lat: 33.8303, lng: -118.1143 }, // Long Beach, CA
            "90810": { lat: 33.8108, lng: -118.2250 }, // Long Beach, CA
            "90813": { lat: 33.7866, lng: -118.1873 }, // Long Beach, CA
            "90814": { lat: 33.7701, lng: -118.1471 }, // Long Beach, CA
            "90815": { lat: 33.7941, lng: -118.1306 }, // Long Beach, CA
            
            // Venice Beach
            "90291": { lat: 33.9850, lng: -118.4695 }, // Venice, CA
            
            // Culver City
            "90230": { lat: 34.0211, lng: -118.3965 }, // Culver City, CA
            "90232": { lat: 34.0211, lng: -118.3965 }, // Culver City, CA
            
            // Hawthorne
            "90250": { lat: 33.9164, lng: -118.3525 }, // Hawthorne, CA
            
            // Ames, IA
            "50010": { lat: 42.0308, lng: -93.6319 }, // Ames, IA
            "50011": { lat: 42.0308, lng: -93.6319 }, // Ames, IA (Iowa State University)
            "50014": { lat: 42.0308, lng: -93.6319 }, // Ames, IA
            
            // New York City
            "10001": { lat: 40.7503, lng: -73.9972 }, // Manhattan, NY
            "10002": { lat: 40.7168, lng: -73.9861 }, // Manhattan, NY
            "10003": { lat: 40.7318, lng: -73.9888 }, // Manhattan, NY
            "10007": { lat: 40.7133, lng: -74.0070 }, // Manhattan, NY
            "10011": { lat: 40.7399, lng: -74.0021 }, // Manhattan, NY
            "10016": { lat: 40.7459, lng: -73.9776 }, // Manhattan, NY
            "10017": { lat: 40.7520, lng: -73.9736 }, // Manhattan, NY
            "10018": { lat: 40.7551, lng: -73.9932 }, // Manhattan, NY
            "10019": { lat: 40.7662, lng: -73.9862 }, // Manhattan, NY
            "10021": { lat: 40.7690, lng: -73.9550 }, // Manhattan, NY
            "10022": { lat: 40.7583, lng: -73.9685 }, // Manhattan, NY
            "10023": { lat: 40.7769, lng: -73.9822 }, // Manhattan, NY
            "10024": { lat: 40.7892, lng: -73.9745 }, // Manhattan, NY
            "10025": { lat: 40.7989, lng: -73.9669 }, // Manhattan, NY
            "10028": { lat: 40.7764, lng: -73.9546 }, // Manhattan, NY
            "10036": { lat: 40.7602, lng: -73.9896 }, // Manhattan, NY
            "10128": { lat: 40.7808, lng: -73.9497 }, // Manhattan, NY
            "11201": { lat: 40.6958, lng: -73.9897 }, // Brooklyn, NY
            "11215": { lat: 40.6710, lng: -73.9860 }, // Brooklyn, NY
            "11217": { lat: 40.6829, lng: -73.9790 }, // Brooklyn, NY
            "11220": { lat: 40.6340, lng: -74.0110 }, // Brooklyn, NY
            "11222": { lat: 40.7277, lng: -73.9443 }, // Brooklyn, NY
            "11231": { lat: 40.6795, lng: -74.0029 }, // Brooklyn, NY
            "11238": { lat: 40.6795, lng: -73.9643 }, // Brooklyn, NY
            "11211": { lat: 40.7121, lng: -73.9538 }, // Brooklyn, NY (Williamsburg)
            "11249": { lat: 40.7121, lng: -73.9538 }, // Brooklyn, NY (Williamsburg)
            "11101": { lat: 40.7464, lng: -73.9394 }, // Queens, NY (Long Island City)
            "11106": { lat: 40.7628, lng: -73.9311 }, // Queens, NY (Astoria)
            "11103": { lat: 40.7628, lng: -73.9125 }, // Queens, NY (Astoria)
            "10451": { lat: 40.8200, lng: -73.9261 }, // Bronx, NY
            "10452": { lat: 40.8367, lng: -73.9172 }, // Bronx, NY
            "10453": { lat: 40.8521, lng: -73.9119 }, // Bronx, NY
            "10301": { lat: 40.6368, lng: -74.1181 }, // Staten Island, NY
            "10304": { lat: 40.6097, lng: -74.0859 }, // Staten Island, NY
            "10305": { lat: 40.5972, lng: -74.0759 }, // Staten Island, NY
            
            // Omaha, NE
            "68102": { lat: 41.2627, lng: -95.9350 }, // Omaha, NE (Downtown)
            "68104": { lat: 41.2917, lng: -96.0066 }, // Omaha, NE
            "68105": { lat: 41.2431, lng: -95.9577 }, // Omaha, NE
            "68106": { lat: 41.2431, lng: -96.0066 }, // Omaha, NE
            "68107": { lat: 41.2139, lng: -95.9577 }, // Omaha, NE
            "68108": { lat: 41.2335, lng: -95.9350 }, // Omaha, NE
            "68110": { lat: 41.2917, lng: -95.9350 }, // Omaha, NE
            "68111": { lat: 41.2917, lng: -95.9577 }, // Omaha, NE
            "68112": { lat: 41.3403, lng: -95.9577 }, // Omaha, NE
            "68114": { lat: 41.2627, lng: -96.0554 }, // Omaha, NE
            "68116": { lat: 41.2917, lng: -96.1531 }, // Omaha, NE
            "68117": { lat: 41.2139, lng: -96.0066 }, // Omaha, NE
            "68118": { lat: 41.2627, lng: -96.1531 }, // Omaha, NE
            "68130": { lat: 41.2335, lng: -96.1531 }, // Omaha, NE
            "68131": { lat: 41.2723, lng: -95.9577 }, // Omaha, NE
            "68132": { lat: 41.2723, lng: -96.0066 }, // Omaha, NE
            "68134": { lat: 41.2917, lng: -96.0554 }, // Omaha, NE
            "68144": { lat: 41.2139, lng: -96.1043 }, // Omaha, NE
            "68154": { lat: 41.2335, lng: -96.1043 }, // Omaha, NE
            
            // Wilmington, DE
            "19801": { lat: 39.7459, lng: -75.5466 }, // Wilmington, DE (Downtown)
            "19802": { lat: 39.7598, lng: -75.5276 }, // Wilmington, DE
            "19803": { lat: 39.7929, lng: -75.5466 }, // Wilmington, DE
            "19804": { lat: 39.7321, lng: -75.5847 }, // Wilmington, DE
            "19805": { lat: 39.7321, lng: -75.5656 }, // Wilmington, DE
            "19806": { lat: 39.7598, lng: -75.5656 }, // Wilmington, DE
            "19807": { lat: 39.7790, lng: -75.5847 }, // Wilmington, DE
            "19808": { lat: 39.7321, lng: -75.6228 }, // Wilmington, DE
            "19809": { lat: 39.7598, lng: -75.4895 }, // Wilmington, DE
            "19810": { lat: 39.8260, lng: -75.5466 }, // Wilmington, DE
            
            // Winston-Salem, NC
            "27101": { lat: 36.0999, lng: -80.2442 }, // Winston-Salem, NC (Downtown)
            "27103": { lat: 36.0638, lng: -80.3218 }, // Winston-Salem, NC
            "27104": { lat: 36.0999, lng: -80.3218 }, // Winston-Salem, NC
            "27105": { lat: 36.1360, lng: -80.2442 }, // Winston-Salem, NC
            "27106": { lat: 36.1360, lng: -80.3218 }, // Winston-Salem, NC
            "27107": { lat: 36.0277, lng: -80.2442 }, // Winston-Salem, NC
            "27127": { lat: 36.0277, lng: -80.3218 }, // Winston-Salem, NC
            
            // San Fernando Valley & Thousand Oaks
            "91301": { lat: 34.1517, lng: -118.7798 }, // Agoura Hills, CA
            "91302": { lat: 34.1478, lng: -118.7126 }, // Calabasas, CA
            "91303": { lat: 34.2011, lng: -118.6031 }, // Canoga Park, CA
            "91304": { lat: 34.2011, lng: -118.6031 }, // Canoga Park, CA
            "91306": { lat: 34.2011, lng: -118.5257 }, // Winnetka, CA
            "91307": { lat: 34.1883, lng: -118.6417 }, // West Hills, CA
            "91311": { lat: 34.2501, lng: -118.6417 }, // Chatsworth, CA
            "91316": { lat: 34.1517, lng: -118.5031 }, // Encino, CA
            "91324": { lat: 34.2351, lng: -118.5386 }, // Northridge, CA
            "91325": { lat: 34.2351, lng: -118.5386 }, // Northridge, CA
            "91330": { lat: 34.2410, lng: -118.5300 }, // Northridge, CA (CSUN)
            "91335": { lat: 34.2011, lng: -118.5386 }, // Reseda, CA
            "91340": { lat: 34.2881, lng: -118.4386 }, // San Fernando, CA
            "91342": { lat: 34.3231, lng: -118.4257 }, // Sylmar, CA
            "91343": { lat: 34.2351, lng: -118.4644 }, // North Hills, CA
            "91344": { lat: 34.2881, lng: -118.5031 }, // Granada Hills, CA
            "91345": { lat: 34.2881, lng: -118.4644 }, // Mission Hills, CA
            "91350": { lat: 34.4531, lng: -118.5257 }, // Santa Clarita, CA
            "91351": { lat: 34.4531, lng: -118.5257 }, // Santa Clarita, CA
            "91352": { lat: 34.2351, lng: -118.3870 }, // Sun Valley, CA
            "91354": { lat: 34.4531, lng: -118.5257 }, // Santa Clarita, CA
            "91355": { lat: 34.4531, lng: -118.5644 }, // Santa Clarita, CA
            "91356": { lat: 34.1517, lng: -118.5386 }, // Tarzana, CA
            "91360": { lat: 34.1700, lng: -118.8500 }, // Thousand Oaks, CA
            "91361": { lat: 34.1478, lng: -118.8185 }, // Westlake Village, CA
            "91362": { lat: 34.1700, lng: -118.8500 }, // Thousand Oaks, CA
            "91364": { lat: 34.1517, lng: -118.5644 }, // Woodland Hills, CA
            "91367": { lat: 34.1517, lng: -118.6031 }, // Woodland Hills, CA
            "91401": { lat: 34.1794, lng: -118.4386 }, // Van Nuys, CA
            "91402": { lat: 34.2351, lng: -118.4386 }, // Panorama City, CA
            "91403": { lat: 34.1517, lng: -118.4644 }, // Sherman Oaks, CA
            "91405": { lat: 34.1794, lng: -118.4386 }, // Van Nuys, CA
            "91406": { lat: 34.1794, lng: -118.5031 }, // Van Nuys, CA
            "91411": { lat: 34.1794, lng: -118.4644 }, // Van Nuys, CA
            "91423": { lat: 34.1517, lng: -118.4386 }, // Sherman Oaks, CA
            "91436": { lat: 34.1517, lng: -118.4902 }, // Encino, CA
            
            // Las Vegas
            "89101": { lat: 36.1750, lng: -115.1372 }, // Las Vegas, NV
            "89102": { lat: 36.1750, lng: -115.1933 }, // Las Vegas, NV
            "89103": { lat: 36.1147, lng: -115.1933 }, // Las Vegas, NV
            "89104": { lat: 36.1750, lng: -115.0811 }, // Las Vegas, NV
            "89106": { lat: 36.2075, lng: -115.1653 }, // Las Vegas, NV
            "89107": { lat: 36.1750, lng: -115.2214 }, // Las Vegas, NV
            "89108": { lat: 36.2075, lng: -115.2214 }, // Las Vegas, NV
            "89109": { lat: 36.1147, lng: -115.1653 }, // Las Vegas, NV (The Strip)
            "89110": { lat: 36.1750, lng: -115.0531 }, // Las Vegas, NV
            "89113": { lat: 36.0869, lng: -115.2495 }, // Las Vegas, NV
            "89117": { lat: 36.1425, lng: -115.2775 }, // Las Vegas, NV

            // San Diego County - Poway
            "92064": { lat: 32.9628, lng: -117.0359 }, // Poway, CA

            // Orange County - San Juan Capistrano
            "92675": { lat: 33.5017, lng: -117.6628 }, // San Juan Capistrano, CA

            // Silicon Valley Area
            "94301": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94302": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94303": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94304": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "94305": { lat: 37.4419, lng: -122.1430 }, // Stanford, CA
            "94306": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
            "95014": { lat: 37.3230, lng: -122.0322 }, // Cupertino, CA
            "95051": { lat: 37.3688, lng: -121.9678 }, // Santa Clara, CA
            "95054": { lat: 37.3688, lng: -121.9678 }, // Santa Clara, CA
            "95070": { lat: 37.3541, lng: -122.0522 }, // Saratoga, CA
            "95110": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95111": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95112": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95113": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95116": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95117": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95118": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95119": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95120": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95121": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95122": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95123": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95124": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95125": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95126": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95127": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95128": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95129": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95130": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95131": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95132": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95133": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95134": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95135": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95136": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95138": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "95139": { lat: 37.3382, lng: -121.8863 }, // San Jose, CA
            "94085": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94086": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94087": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94089": { lat: 37.4224, lng: -122.0856 }, // Sunnyvale, CA
            "94041": { lat: 37.3861, lng: -122.0839 }, // Mountain View, CA
            "94043": { lat: 37.3861, lng: -122.0839 }, // Mountain View, CA
            "94024": { lat: 37.4852, lng: -122.1483 }, // Los Altos, CA
            "94022": { lat: 37.4852, lng: -122.1483 }, // Los Altos, CA
            "94040": { lat: 37.3861, lng: -122.0839 }, // Mountain View, CA
            "94035": { lat: 37.4419, lng: -122.1430 }, // Milpitas, CA
            "95035": { lat: 37.4323, lng: -121.9018 }, // Milpitas, CA

            // Ventura County
            "93001": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93003": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93004": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93006": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
            "93009": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA

            // Seattle Metropolitan Area
            // Seattle
            "98101": { lat: 47.6062, lng: -122.3321 }, // Seattle, WA (Downtown)
            "98102": { lat: 47.6205, lng: -122.3212 }, // Seattle, WA (Capitol Hill)
            "98103": { lat: 47.6740, lng: -122.3419 }, // Seattle, WA (Fremont/Wallingford)
            "98104": { lat: 47.6021, lng: -122.3321 }, // Seattle, WA (Pioneer Square)
            "98105": { lat: 47.6606, lng: -122.3031 }, // Seattle, WA (University District)
            "98106": { lat: 47.5287, lng: -122.3487 }, // Seattle, WA (White Center)
            "98107": { lat: 47.6684, lng: -122.3762 }, // Seattle, WA (Ballard)
            "98108": { lat: 47.5287, lng: -122.3212 }, // Seattle, WA (South Park)
            "98109": { lat: 47.6205, lng: -122.3487 }, // Seattle, WA (South Lake Union)
            "98112": { lat: 47.6205, lng: -122.3031 }, // Seattle, WA (Capitol Hill East)
            "98115": { lat: 47.6897, lng: -122.3031 }, // Seattle, WA (Northgate)
            "98116": { lat: 47.5649, lng: -122.3762 }, // Seattle, WA (West Seattle)
            "98117": { lat: 47.6897, lng: -122.3762 }, // Seattle, WA (Ballard North)
            "98118": { lat: 47.5287, lng: -122.2762 }, // Seattle, WA (Rainier Valley)
            "98119": { lat: 47.6354, lng: -122.3762 }, // Seattle, WA (Interbay)
            "98121": { lat: 47.6149, lng: -122.3487 }, // Seattle, WA (Belltown)
            "98122": { lat: 47.6062, lng: -122.3031 }, // Seattle, WA (Central District)
            "98125": { lat: 47.7231, lng: -122.3031 }, // Seattle, WA (Northgate North)
            "98126": { lat: 47.5649, lng: -122.3487 }, // Seattle, WA (West Seattle South)
            "98133": { lat: 47.7231, lng: -122.3487 }, // Seattle, WA (North Seattle)
            "98134": { lat: 47.5649, lng: -122.3212 }, // Seattle, WA (SODO)
            "98136": { lat: 47.5287, lng: -122.3762 }, // Seattle, WA (Burien/Highline)
            "98144": { lat: 47.5649, lng: -122.2762 }, // Seattle, WA (Mount Baker)
            "98146": { lat: 47.5287, lng: -122.3487 }, // Seattle, WA (Burien)
            "98148": { lat: 47.4287, lng: -122.3212 }, // Seattle, WA (SeaTac)
            "98154": { lat: 47.5649, lng: -122.3321 }, // Seattle, WA (South Seattle)
            "98155": { lat: 47.7231, lng: -122.2762 }, // Seattle, WA (Shoreline)
            "98158": { lat: 47.4649, lng: -122.2762 }, // Seattle, WA (Tukwila)
            "98164": { lat: 47.5649, lng: -122.3321 }, // Seattle, WA (Industrial District)
            "98166": { lat: 47.4649, lng: -122.3487 }, // Seattle, WA (Normandy Park)
            "98168": { lat: 47.4649, lng: -122.3212 }, // Seattle, WA (Tukwila South)
            "98177": { lat: 47.7564, lng: -122.3762 }, // Seattle, WA (Shoreline West)
            "98178": { lat: 47.4649, lng: -122.2762 }, // Seattle, WA (Tukwila East)
            "98188": { lat: 47.4287, lng: -122.2762 }, // Seattle, WA (SeaTac East)
            "98199": { lat: 47.6354, lng: -122.4031 }, // Seattle, WA (Magnolia)

            // Redmond
            "98052": { lat: 47.6740, lng: -122.1215 }, // Redmond, WA
            "98053": { lat: 47.6740, lng: -122.1215 }, // Redmond, WA (Microsoft Campus area)

            // Bellevue
            "98004": { lat: 47.6101, lng: -122.2015 }, // Bellevue, WA (Downtown)
            "98005": { lat: 47.6101, lng: -122.1515 }, // Bellevue, WA (East)
            "98006": { lat: 47.5649, lng: -122.1515 }, // Bellevue, WA (South)
            "98007": { lat: 47.6101, lng: -122.1215 }, // Bellevue, WA (Crossroads)
            "98008": { lat: 47.6354, lng: -122.1515 }, // Bellevue, WA (North)

            // Medina
            "98039": { lat: 47.6240, lng: -122.2304 }, // Medina, WA

            // Florida - Fort Myers Area (Lee County)
            "33901": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL (Downtown)
            "33902": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33903": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33904": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33905": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33906": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33907": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33908": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33909": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33912": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33913": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33914": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33915": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33916": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33917": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33918": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33919": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33920": { lat: 26.6406, lng: -81.8723 }, // Fort Myers, FL
            "33921": { lat: 26.5628, lng: -81.9495 }, // Estero, FL
            "33922": { lat: 26.4614, lng: -81.8081 }, // Fort Myers Beach, FL
            "33924": { lat: 26.4614, lng: -81.8081 }, // Fort Myers Beach, FL
            "33928": { lat: 26.5312, lng: -82.0251 }, // Bonita Springs, FL
            "33931": { lat: 26.3587, lng: -81.8723 }, // Lehigh Acres, FL
            "33936": { lat: 26.3587, lng: -81.8723 }, // Lehigh Acres, FL
            "33957": { lat: 26.7153, lng: -81.7787 }, // North Fort Myers, FL
            "33966": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33967": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33971": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33972": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33973": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33974": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33976": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33990": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33991": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL
            "33993": { lat: 26.4801, lng: -81.7787 }, // Cape Coral, FL

            // Florida - Sarasota Area (Sarasota County)
            "34201": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34202": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34203": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34205": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34207": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34208": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34209": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34210": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34211": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34212": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34215": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34216": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34217": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34219": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34221": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34222": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34223": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34224": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34228": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34229": { lat: 27.4989, lng: -82.5748 }, // Bradenton, FL
            "34230": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34231": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34232": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34233": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34234": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34235": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34236": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34237": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34238": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34239": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34240": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34241": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34242": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34243": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34251": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34260": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34264": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34265": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34266": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34267": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34268": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34269": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34270": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34274": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34275": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34276": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34277": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34278": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34280": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34281": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34282": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34284": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34285": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34286": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34287": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34288": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34289": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34290": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34291": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34292": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34293": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL
            "34295": { lat: 27.3364, lng: -82.5307 }, // Sarasota, FL

            // Florida - Clearwater/Tampa Bay Area (Pinellas County)
            "33755": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33756": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33759": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33760": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33761": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33762": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33763": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33764": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33765": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33767": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33770": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33771": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33772": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33773": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33774": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33775": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33776": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33777": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33778": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33779": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33780": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33781": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33782": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33784": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33785": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33786": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL
            "33787": { lat: 27.9659, lng: -82.8001 }, // Clearwater, FL

            // San Francisco, CA
            "94102": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Downtown/Civic Center)
            "94103": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (SOMA)
            "94104": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Financial District)
            "94105": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (SOMA/Rincon Hill)
            "94107": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Potrero Hill)
            "94108": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Chinatown/Nob Hill)
            "94109": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Polk Gulch/Russian Hill)
            "94110": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Mission District)
            "94111": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Financial District)
            "94112": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Outer Mission)
            "94114": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Castro/Noe Valley)
            "94115": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Pacific Heights)
            "94116": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Sunset District)
            "94117": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Haight-Ashbury)
            "94118": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Richmond District)
            "94121": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Richmond District)
            "94122": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Sunset District)
            "94123": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Marina District)
            "94124": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Bayview)
            "94127": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (West Portal)
            "94129": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Presidio)
            "94130": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Treasure Island)
            "94131": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Glen Park)
            "94132": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Lake Merced)
            "94133": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (North Beach)
            "94134": { lat: 37.7749, lng: -122.4194 }, // San Francisco, CA (Visitacion Valley)
            "94158": { lat: 37.7749, lng: -122.4194 } // San Francisco, CA (Mission Bay)
          };
          
          // Check if the ZIP code exists in the hardcoded mapping
          if (zipCoordinates[zipCode]) {
            const { lat, lng } = zipCoordinates[zipCode];
            window.LAT = lat;
            window.LNG = lng;
            window.ZIP_CODE = zipCode;
            zipDisplay.textContent = zipCode;
            coordsDisplay.textContent = `${lat}, ${lng}`;
            statusMessage.textContent = `Coordinates found: ${window.LAT}, ${window.LNG}`;
            statusMessage.style.color = "#333";
            return { lat, lng };
          } else {
            throw new Error("ZIP code not found in local database");
          }
        } catch (error) {
          console.error("Error geocoding ZIP code:", error);
          statusMessage.textContent = `Error looking up coordinates: ${error.message}. Using default ZIP ${window.ZIP_CODE}.`;
          statusMessage.style.color = "#d32f2f";
          // Reset input to current valid ZIP
          zipCodeInput.value = window.ZIP_CODE;
          return null;
        }
      }

      // Add event listener to ZIP code input for real-time geocoding
      zipCodeInput.addEventListener("blur", async function() {
        const newZip = this.value.trim();
        if (newZip && /^\d{5}$/.test(newZip) && newZip !== window.ZIP_CODE) {
          await window.geocodeZipCode(newZip);
        }
      });

      // Also handle Enter key on ZIP input
      zipCodeInput.addEventListener("keydown", async function(e) {
        if (e.key === "Enter") {
          const newZip = this.value.trim();
          if (newZip && /^\d{5}$/.test(newZip) && newZip !== window.ZIP_CODE) {
            await window.geocodeZipCode(newZip);
          }
        }
      });
    })();
  </script>


  <script>
    (function () {
      // ======================================================
      // User Categories Storage Helpers
      // ======================================================
      function loadUserCategories() {
        try {
          const stored = localStorage.getItem("userCategories");
          if (!stored) return {};
          return JSON.parse(stored) || {};
        } catch (err) {
          console.error("Error loading user categories:", err);
          return {};
        }
      }
      
      function saveUserCategories(userCategories) {
        try {
          localStorage.setItem("userCategories", JSON.stringify(userCategories));
          return true;
        } catch (err) {
          console.error("Error saving user categories:", err);
          return false;
        }
      }

      // ======================================================
      // User Groups Storage Helpers
      // ======================================================
      function loadUserGroups() {
        try {
          const stored = localStorage.getItem("userGroups");
          if (!stored) return [];
          return JSON.parse(stored) || [];
        } catch (err) {
          console.error("Error loading user groups:", err);
          return [];
        }
      }

      function saveUserGroups(userGroups) {
        try {
          localStorage.setItem("userGroups", JSON.stringify(userGroups));
          return true;
        } catch (err) {
          console.error("Error saving user groups:", err);
          return false;
        }
      }

      // ======================================================
      // Hidden Groups Storage Helpers
      // ======================================================
      function loadHiddenGroups() {
        try {
          const stored = localStorage.getItem("hiddenGroups");
          if (!stored) return [];
          return JSON.parse(stored) || [];
        } catch (err) {
          console.error("Error loading hidden groups:", err);
          return [];
        }
      }

      function saveHiddenGroups(hiddenGroups) {
        try {
          localStorage.setItem("hiddenGroups", JSON.stringify(hiddenGroups));
          return true;
        } catch (err) {
          console.error("Error saving hidden groups:", err);
          return false;
        }
      }

      // ======================================================
      // Delete Group Functionality
      // ======================================================
      function deleteGroup(groupKey) {
        try {
          // 1. Remove all user-defined categories in this group
          const userCategories = loadUserCategories();
          const categoriesToDelete = [];

          Object.entries(userCategories).forEach(([filename, filters]) => {
            if (filters[0] && filters[0].key === groupKey) {
              categoriesToDelete.push(filename);
            }
          });

          // Delete the user categories
          categoriesToDelete.forEach(filename => {
            delete userCategories[filename];
          });
          saveUserCategories(userCategories);

          // 2. Remove the group from user groups if it's a custom group
          const userGroups = loadUserGroups();
          const groupIndex = userGroups.indexOf(groupKey);
          if (groupIndex > -1) {
            userGroups.splice(groupIndex, 1);
            saveUserGroups(userGroups);
          }

          // 3. For built-in groups, we'll hide them by adding to a "hidden groups" list
          const hiddenGroups = loadHiddenGroups();
          if (!hiddenGroups.includes(groupKey)) {
            hiddenGroups.push(groupKey);
            saveHiddenGroups(hiddenGroups);
          }

          // 4. Refresh the UI
          initializeCategoryCheckboxes();

          // 5. Show success message
          console.log(`Group "${groupKey}" deleted successfully`);

        } catch (error) {
          console.error("Error deleting group:", error);
          alert("An error occurred while deleting the group. Please try again.");
        }
      }

      // ======================================================
      // Additional Categories
      // ======================================================
      const CATEGORIES = {
        "liquor_distributors.csv":     [{ key: "shop",     value: "alcohol" }],
        "manufacturers.csv":           [{ key: "industrial", value: "manufacturer" }],
        "hair_salons.csv":             [{ key: "shop",     value: "hairdresser" }],
        "nail_shops.csv":              [{ key: "shop",     value: "beauty" }],
        "auto_body_shops.csv":         [{ key: "shop",     value: "car_repair" }], // same as auto_mechanic
        "montessori_schools.csv":      [{ key: "amenity",  value: "school" }, { key: "school:level", value: "pre_school" }],
        "wealth_management.csv":       [{ key: "office",   value: "financial" }],
        "venture_capital.csv":         [{ key: "office",   value: "financial" }],
        "stock_brokers.csv":           [{ key: "office",   value: "financial" }],
        "medical_practices.csv":       [{ key: "amenity",  value: "clinic" }],
        "hospitals.csv":               [{ key: "amenity",  value: "hospital" }],
        "dental_practices.csv":        [{ key: "amenity",  value: "dentist" }],
        "veterinary_clinics.csv":      [{ key: "amenity",  value: "veterinary" }],
        "nursing_homes.csv":           [{ key: "amenity",  value: "nursing_home" }],
        "assisted_living.csv":         [{ key: "amenity",  value: "social_facility" }],
        "pharmaceutical_companies.csv": [{ key: "shop",    value: "pharmacy" }],
        "manufacturing_industrial.csv": [{ key: "industrial", value: "manufacturer" }],
        "auto_dealerships.csv":        [{ key: "shop",     value: "car" }],
        "banks_financial.csv":         [{ key: "amenity",  value: "bank" }],
        "accounting_firms.csv":        [{ key: "office",   value: "accountant" }],
        "insurance_agencies.csv":      [{ key: "office",   value: "insurance" }],
        "real_estate_agencies.csv":    [{ key: "office",   value: "real_estate" }],
        "educational_institutions.csv": [{ key: "amenity", value: "school" }],
        "daycare_centers.csv":         [{ key: "amenity",  value: "childcare" }],
        "nonprofits.csv":              [{ key: "office",   value: "non_profit_organization" }],
        "government_agencies.csv":     [{ key: "office",   value: "government" }],
        "retail_stores.csv":           [{ key: "shop" }], // generic shop filter
        "restaurants.csv":             [{ key: "amenity",  value: "restaurant" }],
        "hotels_motels.csv":           [{ key: "tourism",  value: "hotel" }],
        "logistics_transport.csv":     [{ key: "office",   value: "logistics" }],
        "engineering_firms.csv":       [{ key: "office",   value: "engineering" }],
        "architecture_design.csv":     [{ key: "office",   value: "architect" }],
        "consulting_firms.csv":        [{ key: "office",   value: "consulting" }],
        "marketing_agencies.csv":      [{ key: "office",   value: "marketing" }],
        "media_production.csv":        [{ key: "office",   value: "media" }],
        "telecom_voip.csv":            [{ key: "office",   value: "telecom" }],
        "energy_utility.csv":          [{ key: "office",   value: "utility" }],
        "gyms_fitness.csv":            [{ key: "leisure",  value: "fitness_centre" }],
        "salons_spas.csv":             [{ key: "shop",     value: "beauty" }],
        "agriculture_farming.csv":     [{ key: "landuse",  value: "farmland" }],
        "aerospace_aviation.csv":      [{ key: "aeroway" }],
        "coworking_spaces.csv":        [{ key: "office",   value: "coworking" }],
      };
    const ADDITIONAL_CATEGORIES = {
        "breweries.csv":          [{ key: "craft",   value: "brewery" }],
        "wineries.csv":           [{ key: "craft",   value: "winery" }],
        "distilleries.csv":       [{ key: "craft",   value: "distillery" }],
        "cafes.csv":              [{ key: "amenity", value: "cafe" }],
        "bars.csv":               [{ key: "amenity", value: "bar" }],
        "pubs.csv":               [{ key: "amenity", value: "pub" }],
        "nightclubs.csv":         [{ key: "amenity", value: "nightclub" }],
        "cinemas.csv":            [{ key: "amenity", value: "cinema" }],
        "art_galleries.csv":      [{ key: "tourism", value: "gallery" }],
        "museums.csv":            [{ key: "tourism", value: "museum" }],
        "bookstores.csv":         [{ key: "shop",    value: "books" }],
        "hardware_stores.csv":    [{ key: "shop",    value: "hardware" }],
        "electronics_stores.csv": [{ key: "shop",    value: "electronics" }],
        "furniture_stores.csv":   [{ key: "shop",    value: "furniture" }],
        "pet_stores.csv":         [{ key: "shop",    value: "pet" }],
        "bakeries.csv":           [{ key: "shop",    value: "bakery" }],
        "convenience_stores.csv": [{ key: "shop",    value: "convenience" }],
        "grocery_stores.csv":     [{ key: "shop",    value: "supermarket" }],
        "dry_cleaners.csv":       [{ key: "shop",    value: "laundry" }],
        "printing_shops.csv":     [{ key: "shop",    value: "printing" }],
        "tattoo_parlors.csv":     [{ key: "shop",    value: "tattoo" }],
        "car_rental.csv":         [{ key: "amenity", value: "car_rental" }],
        "fuel_stations.csv":      [{ key: "amenity", value: "fuel" }]
    };
      const CSV_HEADER = [
        "Name",
        "Street Number",
        "Street Name",
        "City",
        "State",
        "ZIP+4",
        "Phone Number",
        "Fax Number",
        "Email Address",
        "Website",
        "Contact Name(s)"
      ];

      // ======================================================
      // DOM Elements
      // ======================================================
      const runBtn = document.getElementById("runBtn");
      const logDiv = document.getElementById("log");
      const downloadsDiv = document.getElementById("downloads");
      const selectAllCategoriesCheckbox = document.getElementById("selectAllCategories");
      const allCategoriesContainer = document.getElementById("allCategories");
      const downloadAllBtn = document.getElementById("downloadAllBtn");

      // ZIP code and radius elements (needed for runExport)
      const zipCodeInput = document.getElementById("zipCode");
      const zipDisplay = document.getElementById("zipDisplay");

      // Header new entry container for adding groups
      const headerNewEntryContainer = document.getElementById("headerNewEntryContainer");



      
      // ======================================================
      // Build All Categories (including user-defined ones)
      // ======================================================
      function buildAllCategories() {
        return {
          ...CATEGORIES,
          ...ADDITIONAL_CATEGORIES,
          ...loadUserCategories()
        };
      }

      // ======================================================
      // Build All Groups (including user-defined ones, excluding hidden)
      // ======================================================
      function buildAllGroups() {
        // 1. Collect all built-in "key" group names:
        const builtInKeys = new Set();
        Object.values({ ...CATEGORIES, ...ADDITIONAL_CATEGORIES }).forEach(filters => {
          filters.forEach(f => {
            if (f.key) builtInKeys.add(f.key);
          });
        });
        // 2. Get any user-defined group names:
        const userGroups = loadUserGroups();
        // 3. Get hidden groups to filter out:
        const hiddenGroups = loadHiddenGroups();
        // 4. Merge built-in keys and userGroups (ensure unique), excluding hidden:
        return [
          ...Array.from(builtInKeys),
          ...userGroups.filter(g => !builtInKeys.has(g))
        ].filter(group => !hiddenGroups.includes(group));
      }
      
      // ======================================================
      // Initialize Category Checkboxes
      // ======================================================
      function initializeCategoryCheckboxes() {
        // Clear existing categories
        allCategoriesContainer.innerHTML = "";
        
        // Build all categories (including user-defined ones)
        const ALL_CATEGORIES = buildAllCategories();
        
        // Group categories by their key
        const categoriesByKey = groupCategoriesByKey(ALL_CATEGORIES);
        
        // Populate categories by key
        populateCategoriesByKey(categoriesByKey, allCategoriesContainer);
        
        // Set up event listeners for checkbox selection
        setupCheckboxEventListeners();
      }
      
      // Group categories by their key (amenity, shop, office, etc.)
      function groupCategoriesByKey(categoriesObj) {
        const groupedCategories = {};
        const hiddenGroups = loadHiddenGroups();

        Object.entries(categoriesObj).forEach(([filename, filters]) => {
          // Use the first filter's key as the group key
          const groupKey = filters[0].key;

          // Skip hidden groups
          if (hiddenGroups.includes(groupKey)) {
            return;
          }

          if (!groupedCategories[groupKey]) {
            groupedCategories[groupKey] = [];
          }

          groupedCategories[groupKey].push({
            filename,
            filters
          });
        });

        // Ensure all user-defined groups appear even if they have no categories (but not hidden ones)
        const userGroups = loadUserGroups();
        userGroups.forEach(groupKey => {
          if (!groupedCategories[groupKey] && !hiddenGroups.includes(groupKey)) {
            groupedCategories[groupKey] = [];
          }
        });

        // Sort the keys alphabetically
        return Object.fromEntries(
          Object.entries(groupedCategories).sort((a, b) => a[0].localeCompare(b[0]))
        );
      }
      
      // Populate categories grouped by key
      function populateCategoriesByKey(groupedCategories, container) {
        // Create a flex container for all key groups
        const keyGroupsContainer = document.createElement('div');
        keyGroupsContainer.className = 'key-groups-container';
        container.appendChild(keyGroupsContainer);
        
        Object.entries(groupedCategories).forEach(([key, categories]) => {
          // Create a group for this key
          const keyGroup = document.createElement('div');
          keyGroup.className = 'key-group';
          
          // Create header for this key
          const keyHeader = document.createElement('div');
          keyHeader.className = 'key-header';
          keyHeader.innerHTML = `
            <label>
              <input type="checkbox" class="select-key" data-key="${key}">
              ${key.charAt(0).toUpperCase() + key.slice(1)} (${categories.length})
            </label>
            <span class="group-delete-btn" data-key="${key}" title="Delete Group">&times;</span>
            <span class="add-new" data-key="${key}" title="Add New Category">+</span>
          `;
          keyGroup.appendChild(keyHeader);
          
          // Create container for items in this key
          const keyItems = document.createElement('div');
          keyItems.className = 'key-items';
          
          // Determine number of columns based on item count
          if (categories.length <= 9) {
            keyItems.classList.add('columns-1');
          } else if (categories.length <= 18) {
            keyItems.classList.add('columns-2');
          } else {
            keyItems.classList.add('columns-3');
          }
          
          // Add scrollbar if more than 27 items
          if (categories.length > 27) {
            keyItems.classList.add('scrollable');
          }
          
          // Sort categories alphabetically within this key
          categories.sort((a, b) => a.filename.localeCompare(b.filename));
          
          // Add each category in this key
          categories.forEach(({ filename }) => {
            const displayName = filename.replace('.csv', '').replace(/_/g, ' ');
            const item = document.createElement('div');
            item.className = 'category-item';
            item.innerHTML = `
              <label>
                <input type="checkbox" class="category-checkbox"
                       data-key="${key}"
                       data-filename="${filename}">
                <span>${displayName}</span>
              </label>
              <span class="remove-btn" title="Remove">&times;</span>
            `;
            keyItems.appendChild(item);
          });
          
          keyGroup.appendChild(keyItems);
          keyGroupsContainer.appendChild(keyGroup);
        });
      }
      

      
      // ======================================================
      // Setup Checkbox Event Listeners
      // ======================================================
      // Flag to track if main event listeners are set up
      let mainEventListenersSetup = false;

      function setupCheckboxEventListeners() {
        // Set up main event listener only once
        if (!mainEventListenersSetup) {
          selectAllCategoriesCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            document.querySelectorAll('.category-checkbox').forEach(checkbox => {
              checkbox.checked = isChecked;
            });
            document.querySelectorAll('.select-key').forEach(checkbox => {
              checkbox.checked = isChecked;
            });
          });
          mainEventListenersSetup = true;
        }

        // Use event delegation for key group checkboxes and individual checkboxes
        // Remove any existing delegated listeners first
        allCategoriesContainer.removeEventListener('change', handleCategoryChange);
        allCategoriesContainer.addEventListener('change', handleCategoryChange);
      }

      function handleCategoryChange(e) {
        if (e.target.classList.contains('select-key')) {
          const key = e.target.dataset.key;
          const isChecked = e.target.checked;

          document.querySelectorAll(`.category-checkbox[data-key="${key}"]`).forEach(cb => {
            cb.checked = isChecked;
          });

          updateSelectAllCheckbox();
        } else if (e.target.classList.contains('category-checkbox')) {
          updateKeyGroupCheckbox(e.target.dataset.key);
          updateSelectAllCheckbox();
        }
      }
      
      // Update key group checkbox based on individual selections
      function updateKeyGroupCheckbox(key) {
        const keyCheckbox = document.querySelector(`.select-key[data-key="${key}"]`);
        const keyItems = document.querySelectorAll(`.category-checkbox[data-key="${key}"]`);
        
        const allChecked = Array.from(keyItems).every(cb => cb.checked);
        const someChecked = Array.from(keyItems).some(cb => cb.checked);
        
        keyCheckbox.checked = allChecked;
        keyCheckbox.indeterminate = someChecked && !allChecked;
      }
      
      // Update the "Select All" checkbox based on all selections
      function updateSelectAllCheckbox() {
        const allCheckboxes = document.querySelectorAll('.category-checkbox');
        const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
        const someChecked = Array.from(allCheckboxes).some(cb => cb.checked);
        
        selectAllCategoriesCheckbox.checked = allChecked;
        selectAllCategoriesCheckbox.indeterminate = someChecked && !allChecked;
      }

      // ======================================================
      // Logging Utility
      // ======================================================
      function log(message) {
        const timeStamp = new Date().toLocaleTimeString();
        logDiv.textContent += `[${timeStamp}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
      }

      // ======================================================
      // Delay Helper
      // ======================================================
      function delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
      }

      // ======================================================
      // Build Overpass QL Query for one category
      // ======================================================
      function buildOverpassQuery(filters) {
        // Build filter clauses for the Overpass query
        const filterClauses = filters.map(filter => {
          if (filter.key && filter.value) {
            return `["${filter.key}"="${filter.value}"]`;
          } else if (filter.key) {
            return `["${filter.key}"]`;
          }
          return "";
        }).join("");

        // Get the current coordinates and radius (to ensure we use the latest values)
        const currentLat = window.LAT;
        const currentLng = window.LNG;
        const currentRadius = window.RADIUS;

        // Around: RADIUS meters around LAT,LNG
        return `
[out:json][timeout:25];
(
  node${filterClauses}(around:${currentRadius},${currentLat},${currentLng});
  way${filterClauses}(around:${currentRadius},${currentLat},${currentLng});
  relation${filterClauses}(around:${currentRadius},${currentLat},${currentLng});
);
out center tags;`;
      }

      // ======================================================
      // Fetch from Overpass API
      // ======================================================
      async function fetchOverpass(query) {
        const url = "https://overpass-api.de/api/interpreter";
        const resp = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          body: new URLSearchParams({ data: query })
        });
        if (!resp.ok) {
          throw new Error(`Overpass error: ${resp.status} ${resp.statusText}`);
        }
        return resp.json();
      }

      // ======================================================
      // Parse OSM tags into address fields
      // ======================================================
      function parseOsmTags(tags = {}) {
        // OSM address tags:
        //   addr:housenumber, addr:street, addr:city, addr:state, addr:postcode, contact:phone, contact:fax, contact:email, website
        const name = tags.name || "";
        const streetNumber = tags["addr:housenumber"] || "";
        const streetName   = tags["addr:street"] || "";
        const city         = tags["addr:city"] || "";
        const state        = tags["addr:state"] || "";
        // OSM typically doesn’t include ZIP+4; store full postcode if available
        const postcode     = tags["addr:postcode"] || "";
        const phone        = tags["contact:phone"] || tags.phone || "";
        const fax          = tags["contact:fax"] || "";
        const email        = tags["contact:email"] || "";
        const website      = tags["contact:website"] || tags.website || "";
        const contacts     = ""; // Not stored in standard OSM tags
        return {
          name,
          streetNumber,
          streetName,
          city,
          state,
          postcode,
          phone,
          fax,
          email,
          website,
          contacts
        };
      }

      // ======================================================
      // CSV escaping
      // ======================================================
      function escapeCSV(value) {
        if (value == null) return "";
        const str = value.toString();
        if (str.includes(",") || str.includes('"') || str.includes("\n")) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      }

      function generateCSVString(rows) {
        return rows.map(row => row.map(escapeCSV).join(",")).join("\r\n");
      }

      // ======================================================
      // Process a single category: fetch OSM data, build CSV rows, provide download link
      // ======================================================
      async function processCategory(filename, filters) {
        log(`→ Starting category: ${filename}`);
        const rows = [CSV_HEADER];

        const query = buildOverpassQuery(filters);
        log(`   Querying Overpass for filters: ${JSON.stringify(filters)}`);

        let data;
        try {
          data = await fetchOverpass(query);
        } catch (err) {
          log(`   [!] Overpass fetch error for ${filename}: ${err.message}`);
          return;
        }
        const elements = data.elements || [];
        log(`   Retrieved ${elements.length} elements for ${filename}`);

        for (const el of elements) {
          const tags = el.tags || {};
          const {
            name,
            streetNumber,
            streetName,
            city,
            state,
            postcode,
            phone,
            fax,
            email,
            website,
            contacts
          } = parseOsmTags(tags);
          
          // Skip entries with no name or no address information
          if (!name || (!streetNumber && !streetName && !city)) {
            continue;
          }
          
          rows.push([
            name,
            streetNumber,
            streetName,
            city,
            state,
            postcode,
            phone,
            fax,
            email,
            website,
            contacts
          ]);
        }

        // Skip creating CSV if there are no data rows (only header)
        if (rows.length <= 1) {
          log(`   [i] No valid data for ${filename} - skipping file creation`);
          return;
        }

        const csvContent = generateCSVString(rows);
        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = filename;
        link.textContent = `Download ${filename}`;
        link.className = "download-link";
        downloadsDiv.appendChild(link);

        log(`✔ Finished category: ${filename} (${rows.length - 1} rows)`);
      }

      // ======================================================
      // Main Runner: iterate selected categories sequentially
      // ======================================================
      async function runExport() {
        // Clear log and download links
        logDiv.textContent = "";
        downloadsDiv.innerHTML = "";
        
        // Get selected categories
        const selectedCheckboxes = document.querySelectorAll('.category-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
          log("[!] No categories selected. Please select at least one category.");
          return;
        }
        
        // Always update ZIP code and coordinates before processing
        const currentZip = zipCodeInput.value.trim();
        if (currentZip !== window.ZIP_CODE) {
          if (/^\d{5}$/.test(currentZip)) {
            log(`Updating coordinates for ZIP code: ${currentZip}...`);
            const result = await window.geocodeZipCode(currentZip);
            if (!result) {
              log(`[!] Failed to geocode ZIP ${currentZip}. Using previous ZIP: ${window.ZIP_CODE}`);
              zipCodeInput.value = window.ZIP_CODE; // Reset input to valid ZIP
            } else {
              log(`✓ Successfully updated coordinates for ZIP ${window.ZIP_CODE}: ${window.LAT}, ${window.LNG}`);
            }
          } else {
            log(`[!] Invalid ZIP code format: ${currentZip}. Using previous ZIP: ${window.ZIP_CODE}`);
            zipCodeInput.value = window.ZIP_CODE; // Reset input to valid ZIP
          }
        }
        
        runBtn.disabled = true;
        downloadAllBtn.disabled = true;
        log("=== Export Started (Overpass) ===");
        
        // Get fresh merged categories including user-defined ones
        const ALL_CATEGORIES = buildAllCategories();
        
        for (const checkbox of selectedCheckboxes) {
          const filename = checkbox.dataset.filename;
          const filters = ALL_CATEGORIES[filename];
          
          try {
            await processCategory(filename, filters);
            // slight delay to avoid overloading Overpass
            await delay(1000);
          } catch (err) {
            log(`   [!] Error processing ${filename}: ${err.message}`);
          }
        }

        log("=== Export Finished. Download links are above. ===");
        runBtn.disabled = false;
        enableDownloadAllButton();
      }

      // ======================================================
      // Setup Dynamic Add/Remove Functionality
      // ======================================================
      function setupDynamicAddRemove() {
        // Delegate click on the "+" to insert a new-entry row
        allCategoriesContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('add-new')) {
            // 1. Determine which key group we're in:
            const key = e.target.dataset.key;  // e.g. "shop", "amenity", etc.
            // 2. Locate its parent .key-group container and then the .key-items container:
            const keyGroup = e.target.closest('.key-group');
            const keyItems = keyGroup.querySelector('.key-items');

            // 3. If there's already a .new-entry row, do nothing (prevent duplicates)
            if (keyItems.querySelector('.category-item.new-entry')) return;

            // 4. Create a new .category-item.new-entry DIV:
            const newItem = document.createElement('div');
            newItem.className = 'category-item new-entry';
            newItem.innerHTML = `
              <input type="text" class="new-category-input" placeholder="Enter new value">
              <span class="cancel-new" title="Cancel">&times;</span>
              <a href="#" class="ask-chatgpt-link" target="_blank" rel="noopener noreferrer">Ask ChatGPT for OSM tags</a>
            `;
            // 5. Prepend this newItem to keyItems so it appears at the top:
            keyItems.prepend(newItem);

            // 6. Grab references to the input field and the new "Ask ChatGPT" link:
            const inputField = newItem.querySelector('.new-category-input');
            const askLink = newItem.querySelector('.ask-chatgpt-link');

            // 7. Focus the input right away:
            inputField.focus();

            // 8. Define a helper function updateChatGPTLink():
            function updateChatGPTLink() {
              const businessValue = inputField.value.trim();
              let businessCategory = key;  // Always include the group's key
              // Construct the prompt text to ask ChatGPT:
              const promptText =
                `Given a business description of "${businessCategory}` +
                `${businessValue ? ' : ' + businessValue : ''}` +
                `", what is the correct Overpass API (OpenStreetMap) key and value to use when searching for that business? ` +
                `Please reply with a list of valid key:value pairs only, without including any code examples.`;
              // URL-encode the prompt:
              const encodedPrompt = encodeURIComponent(promptText);
              // Set the askLink's href accordingly:
              askLink.href = `https://chat.openai.com/?prompt=${encodedPrompt}`;
            }

            // 9. Bind inputField.addEventListener('input', updateChatGPTLink):
            inputField.addEventListener('input', updateChatGPTLink);

            // Initialize the ChatGPT link with the key only
            updateChatGPTLink();

            // 10. Handle keydown events on inputField:
            inputField.addEventListener('keydown', function(evt) {
              if (evt.key === 'Enter') {
                // a) Grab and trim the new value:
                const newValue = inputField.value.trim();
                if (!newValue) return;  // don't proceed if empty

                // b) Build a filename from newValue (underscores + .csv):
                const filename = newValue.replace(/\s+/g, '_') + '.csv';

                // c) Load userCategories from localStorage:
                const userCategories = loadUserCategories();

                // d) Prevent duplicates: if filename already exists in userCategories or built-in categories, alert:
                if (userCategories[filename] || buildAllCategories()[filename]) {
                  alert('Category already exists.');
                  return;
                }

                // e) Otherwise, save to localStorage:
                userCategories[filename] = [{ key, value: newValue }];
                saveUserCategories(userCategories);

                // f) Reinitialize the category list UI to reflect new addition:
                initializeCategoryCheckboxes();
              } else if (evt.key === 'Escape') {
                // Remove the inline new-entry row without saving
                newItem.remove();
              }
            });

            // 11. Bind click on the cancel icon (.cancel-new) to remove newItem:
            newItem.querySelector('.cancel-new').addEventListener('click', function() {
              newItem.remove();
            });
          }
        });

        // Delegate click on "×" (remove-btn) to delete that category
        allCategoriesContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('remove-btn')) {
            const itemDiv = e.target.closest('.category-item');
            const filename = itemDiv.querySelector('.category-checkbox')?.dataset.filename;
            if (filename && loadUserCategories()[filename]) {
              const userCategories = loadUserCategories();
              delete userCategories[filename];
              saveUserCategories(userCategories);
            }
            itemDiv.remove();
            updateKeyGroupCheckbox(itemDiv.querySelector('.category-checkbox')?.dataset.key);
            updateSelectAllCheckbox();
          }
        });

        // Delegate click on group delete button to delete entire group
        allCategoriesContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('group-delete-btn')) {
            const groupKey = e.target.dataset.key;
            const groupName = groupKey.charAt(0).toUpperCase() + groupKey.slice(1);

            // Show confirmation dialog
            const confirmed = confirm(
              `Are you sure you want to delete the entire "${groupName}" group?\n\n` +
              `This will remove:\n` +
              `• The group itself\n` +
              `• All custom categories in this group\n\n` +
              `Built-in categories will be preserved but the group will be hidden.\n\n` +
              `This action cannot be undone.`
            );

            if (confirmed) {
              deleteGroup(groupKey);
            }
          }
        });
      }

      // ======================================================
      // Setup Dynamic Add/Remove Groups Functionality
      // ======================================================
      function setupDynamicAddRemoveGroups() {
        // 1. Grab the "+" in the categories-header:
        const addGroupBtn = document.querySelector(".categories-header .add-group");
        // 2. Listen for clicks:
        addGroupBtn.addEventListener('click', function() {
          // a) Prevent multiple new-entry rows:
          if (headerNewEntryContainer.querySelector('.header-new-entry')) return;
          // b) Create the inline row:
          const newEntryDiv = document.createElement('div');
          newEntryDiv.className = 'header-new-entry';
          newEntryDiv.innerHTML = `
            <input type="text" class="new-group-input" placeholder="Enter new group name">
            <span class="cancel-new" title="Cancel">&times;</span>
            <a href="#" class="ask-chatgpt-link" target="_blank" rel="noopener noreferrer">Ask ChatGPT for OSM tags</a>
          `;
          headerNewEntryContainer.appendChild(newEntryDiv);
          // c) Grab references:
          const inputField = newEntryDiv.querySelector('.new-group-input');
          const askLink = newEntryDiv.querySelector('.ask-chatgpt-link');
          inputField.focus();
          // d) Build a helper to update the ChatGPT link dynamically:
          function updateGroupChatGPTLink() {
            const groupValue = inputField.value.trim();
            const promptText =
              `Given a group description of "${groupValue}", ` +
              `what is the correct Overpass API (OpenStreetMap) key and value ` +
              `to use when searching for that group? ` +
              `Please reply with a list of valid key:value pairs only, ` +
              `without including any code examples.`;
            const encodedPrompt = encodeURIComponent(promptText);
            askLink.href = `https://chat.openai.com/?prompt=${encodedPrompt}`;
          }
          inputField.addEventListener('input', updateGroupChatGPTLink);
          updateGroupChatGPTLink();
          // e) Handle Enter/Escape keys:
          inputField.addEventListener('keydown', function(evt) {
            if (evt.key === 'Enter') {
              const newGroupName = inputField.value.trim();
              if (!newGroupName) return;
              const userGroups = loadUserGroups();
              // Prevent duplicates against built-in groups or existing userGroups
              if (userGroups.includes(newGroupName) || buildAllGroups().includes(newGroupName)) {
                alert('Group already exists.');
                return;
              }
              userGroups.push(newGroupName);
              saveUserGroups(userGroups);
              // Re-render categories & groups:
              headerNewEntryContainer.innerHTML = "";
              initializeCategoryCheckboxes();
            } else if (evt.key === 'Escape') {
              newEntryDiv.remove();
            }
          });
          // f) Bind the cancel icon:
          newEntryDiv.querySelector('.cancel-new').addEventListener('click', function() {
            newEntryDiv.remove();
          });
        });
      }

      // Initialize the UI
      initializeCategoryCheckboxes();
      setupDynamicAddRemove();
      setupDynamicAddRemoveGroups();

      // ======================================================
      // Reset Categories Functionality
      // ======================================================
      const resetCategoriesLink = document.getElementById("resetCategoriesLink");
      resetCategoriesLink.addEventListener("click", function(e) {
        e.preventDefault();
        // 1. Clear stored user categories, groups, and hidden groups:
        localStorage.removeItem("userCategories");
        localStorage.removeItem("userGroups");
        localStorage.removeItem("hiddenGroups");

        // 2. Remove any header-level new-entry if present:
        const headerContainer = document.getElementById("headerNewEntryContainer");
        headerContainer.innerHTML = "";

        // 3. Remove any category-level new-entry rows:
        document.querySelectorAll(".category-item.new-entry").forEach(item => item.remove());

        // 4. Rebuild category checkboxes from scratch:
        initializeCategoryCheckboxes();
      });

      // ======================================================
      // Restore Hidden Groups Functionality
      // ======================================================
      const restoreGroupsLink = document.getElementById("restoreGroupsLink");
      restoreGroupsLink.addEventListener("click", function(e) {
        e.preventDefault();

        const hiddenGroups = loadHiddenGroups();

        if (hiddenGroups.length === 0) {
          alert("No hidden groups to restore.");
          return;
        }

        // Show confirmation dialog with list of hidden groups
        const groupsList = hiddenGroups.map(group =>
          `• ${group.charAt(0).toUpperCase() + group.slice(1)}`
        ).join('\n');

        const confirmed = confirm(
          `Restore the following hidden groups?\n\n${groupsList}\n\n` +
          `This will make them visible again in the categories list.`
        );

        if (confirmed) {
          // Clear hidden groups
          saveHiddenGroups([]);

          // Refresh the UI
          initializeCategoryCheckboxes();

          alert(`Successfully restored ${hiddenGroups.length} group(s).`);
        }
      });

      // ======================================================
      // Event Listener
      // ======================================================
      runBtn.addEventListener("click", runExport);

      // Enable download all button after export is complete
      function enableDownloadAllButton() {
        downloadAllBtn.disabled = false;
      }

      // Download all files as a single ZIP
      async function downloadAllFiles() {
        log("=== Creating ZIP file with all CSVs ===");
        const links = downloadsDiv.querySelectorAll("a.download-link");
        
        if (links.length === 0) {
          log("[!] No files available to download. Run the export first.");
          return;
        }
        
        // Create new JSZip instance
        const zip = new JSZip();
        
        // Add each CSV to the zip
        for (let i = 0; i < links.length; i++) {
          const link = links[i];
          const filename = link.download;
          log(`Adding ${filename} to ZIP (${i+1}/${links.length})`);
          
          // Fetch the blob from the object URL
          const response = await fetch(link.href);
          const blob = await response.blob();
          
          // Add file to zip
          zip.file(filename, blob);
        }
        
        // Generate the zip file
        log("Generating ZIP file...");
        const zipBlob = await zip.generateAsync({type: "blob"});
        
        // Create download link for the zip
        const zipUrl = URL.createObjectURL(zipBlob);
        const zipLink = document.createElement("a");
        zipLink.href = zipUrl;
        zipLink.download = "business_data.zip";
        
        // Trigger download
        document.body.appendChild(zipLink);
        zipLink.click();
        document.body.removeChild(zipLink);
        
        log("=== ZIP file downloaded ===");
      }

      // Add event listener for download all button
      downloadAllBtn.addEventListener("click", downloadAllFiles);

      // Update radius values in the UI
      document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.radius').forEach(el => {
          el.textContent = RADIUS_MILES;
        });
        document.querySelectorAll('.radius-meters').forEach(el => {
          el.textContent = RADIUS;
        });
      });
    })();

    // ======================================================
    // "Story of This Page" Popup Logic
    // ======================================================
    (function() {
      const openBtn = document.getElementById("openPageStory");
      const overlay = document.getElementById("pageStoryOverlay");
      const closeBtn = document.getElementById("closePageStory");
      const dialog = document.getElementById("pageStoryDialog");

      function openStory(e) {
        if (e) e.preventDefault();
        overlay.classList.remove("hidden-overlay");
        overlay.classList.add("visible-overlay");
        // trap focus on dialog
        dialog.focus();
      }

      function closeStory() {
        overlay.classList.remove("visible-overlay");
        overlay.classList.add("hidden-overlay");
        // Return focus to the opener button
        openBtn.focus();
      }

      openBtn.addEventListener("click", openStory);
      closeBtn.addEventListener("click", closeStory);

      // Close on pressing Esc
      document.addEventListener("keydown", function(e) {
        if (e.key === "Escape" && overlay.classList.contains("visible-overlay")) {
          closeStory();
        }
      });

      // Close when clicking outside the dialog
      overlay.addEventListener("click", function(e) {
        if (e.target === overlay) {
          closeStory();
        }
      });

      // Trap focus inside the dialog when open
      dialog.addEventListener("keydown", function(e) {
        if (e.key === "Tab") {
          const focusableElements = dialog.querySelectorAll("button, [href], input, select, textarea, [tabindex]:not([tabindex='-1'])");
          const firstElement = focusableElements[0];
          const lastElement = focusableElements[focusableElements.length - 1];

          if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      });
    })();

    // ======================================================
    // "Instructions" Popup Logic
    // ======================================================
    (function() {
      const openBtn = document.getElementById("openInstructions");
      const overlay = document.getElementById("instructionsOverlay");
      const closeBtn = document.getElementById("closeInstructions");
      const dialog = document.getElementById("instructionsDialog");
      const contentDiv = document.getElementById("instructionsContent");

      // Enhanced markdown to HTML converter
      function parseMarkdown(markdown) {
        let html = markdown;

        // First, extract and protect code blocks to prevent interference
        const codeBlocks = [];
        let codeBlockIndex = 0;

        // Handle code blocks with language specifiers and without
        html = html.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, lang, code) => {
          const placeholder = `__CODE_BLOCK_${codeBlockIndex}__`;
          // Escape HTML entities in code
          const escapedCode = code
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');

          codeBlocks[codeBlockIndex] = `<pre><code>${escapedCode}</code></pre>`;
          codeBlockIndex++;
          return placeholder;
        });

        // Extract and protect inline code
        const inlineCodes = [];
        let inlineCodeIndex = 0;
        html = html.replace(/`([^`\n]+)`/g, (match, code) => {
          const placeholder = `__INLINE_CODE_${inlineCodeIndex}__`;
          // Escape HTML entities in inline code
          const escapedCode = code
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');

          inlineCodes[inlineCodeIndex] = `<code>${escapedCode}</code>`;
          inlineCodeIndex++;
          return placeholder;
        });

        // Convert headers (order matters - longest first)
        html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>');
        html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
        html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
        html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

        // Convert bold and italic (be careful with order)
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        html = html.replace(/(?<!\*)\*([^*\n]+)\*(?!\*)/g, '<em>$1</em>');

        // Convert links
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

        // Convert horizontal rules
        html = html.replace(/^---$/gm, '<hr>');

        // Split into sections and process
        const sections = html.split(/\n\s*\n/);
        const processedSections = [];

        for (let section of sections) {
          section = section.trim();
          if (!section) continue;

          // Skip if it's already HTML (headers, hr, etc.)
          if (section.startsWith('<h') || section.startsWith('<hr') || section.includes('__CODE_BLOCK_')) {
            processedSections.push(section);
            continue;
          }

          // Handle lists
          const lines = section.split('\n');
          if (lines.some(line => line.trim().match(/^[-*]\s/) || line.trim().match(/^\d+\.\s/))) {
            let listHtml = '';
            let currentList = null;
            let listItems = [];

            for (let line of lines) {
              const trimmed = line.trim();

              if (trimmed.match(/^[-*]\s/)) {
                if (currentList !== 'ul') {
                  if (currentList) listHtml += `</${currentList}>`;
                  listHtml += '<ul>';
                  currentList = 'ul';
                }
                listItems.push(`<li>${trimmed.substring(2)}</li>`);
              } else if (trimmed.match(/^\d+\.\s/)) {
                if (currentList !== 'ol') {
                  if (currentList) listHtml += `</${currentList}>`;
                  listHtml += '<ol>';
                  currentList = 'ol';
                }
                listItems.push(`<li>${trimmed.replace(/^\d+\.\s/, '')}</li>`);
              } else if (trimmed) {
                // Non-list line
                if (currentList) {
                  listHtml += listItems.join('') + `</${currentList}>`;
                  listItems = [];
                  currentList = null;
                }
                listHtml += `<p>${trimmed}</p>`;
              }
            }

            if (currentList) {
              listHtml += listItems.join('') + `</${currentList}>`;
            }

            processedSections.push(listHtml);
          } else {
            // Regular paragraph
            processedSections.push(`<p>${section.replace(/\n/g, '<br>')}</p>`);
          }
        }

        html = processedSections.join('\n');

        // Restore code blocks
        for (let i = 0; i < codeBlocks.length; i++) {
          html = html.replace(`__CODE_BLOCK_${i}__`, codeBlocks[i]);
        }

        // Restore inline code
        for (let i = 0; i < inlineCodes.length; i++) {
          html = html.replace(`__INLINE_CODE_${i}__`, inlineCodes[i]);
        }

        return html;
      }

      async function loadMarkdownContent() {
        try {
          contentDiv.innerHTML = '<div class="loading-message">Loading latest documentation from GitHub...</div>';

          // Add cache-busting parameter to ensure we get the latest version
          const timestamp = new Date().getTime();

          // Try multiple possible README filenames and branches
          const possibleUrls = [
            `https://raw.githubusercontent.com/mytech-today-now/business_search/main/README.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/main/readme.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/main/Readme.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/master/README.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/master/readme.md?t=${timestamp}`,
            `https://raw.githubusercontent.com/mytech-today-now/business_search/master/Readme.md?t=${timestamp}`
          ];

          let response;
          let lastError;
          let attemptedUrls = [];

          // Try each URL until one works
          for (const url of possibleUrls) {
            try {
              console.log(`Attempting to fetch: ${url}`);
              response = await fetch(url);
              attemptedUrls.push(`${url} - Status: ${response.status}`);
              if (response.ok) {
                console.log(`Success! Loaded from: ${url}`);
                break; // Success, exit the loop
              }
              lastError = new Error(`HTTP error! status: ${response.status} for ${url}`);
            } catch (error) {
              lastError = error;
              attemptedUrls.push(`${url} - Error: ${error.message}`);
              console.warn(`Failed to fetch from ${url}:`, error.message);
            }
          }

          if (!response || !response.ok) {
            // Try to fall back to local readme.md file
            console.log('GitHub fetch failed, trying local readme.md...');
            try {
              const localResponse = await fetch(`./readme.md?t=${timestamp}`);
              if (localResponse.ok) {
                console.log('Success! Loaded local readme.md');
                const markdownText = await localResponse.text();
                const htmlContent = parseMarkdown(markdownText);
                contentDiv.innerHTML = `
                  <div class="local-fallback-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-bottom: 20px; border-radius: 4px;">
                    <strong>Note:</strong> Displaying local documentation (GitHub version unavailable)
                  </div>
                  ${htmlContent}
                `;
                return;
              }
            } catch (localError) {
              console.warn('Local fallback also failed:', localError.message);
            }
            throw lastError || new Error('All README URLs failed');
          }

          const markdownText = await response.text();
          const htmlContent = parseMarkdown(markdownText);

          contentDiv.innerHTML = htmlContent;

        } catch (error) {
          console.error('Error loading markdown:', error);
          contentDiv.innerHTML = `
            <div class="error-message">
              <h3>Error Loading Documentation</h3>
              <p>Unable to load the documentation from GitHub. This could be due to:</p>
              <ul>
                <li>Network connectivity issues</li>
                <li>GitHub API rate limiting</li>
                <li>Repository access restrictions</li>
                <li>CORS policy restrictions</li>
              </ul>
              <p>Please try again later or visit the GitHub repository directly:</p>
              <p><a href="https://github.com/mytech-today-now/business_search" target="_blank" rel="noopener noreferrer">View Documentation on GitHub</a></p>
              <p><a href="https://github.com/mytech-today-now/business_search/blob/main/README.md" target="_blank" rel="noopener noreferrer">Direct link to README.md</a></p>
              <p><em>Note: The documentation is automatically loaded from the latest version in the repository.</em></p>
              <details>
                <summary>Technical Details</summary>
                <p><strong>Error:</strong> ${error.message}</p>
                <p><strong>Attempted URLs:</strong></p>
                <ul>
                  ${attemptedUrls.map(url => `<li><code>${url}</code></li>`).join('')}
                </ul>
              </details>
            </div>
          `;
        }
      }

      function openInstructions(e) {
        if (e) e.preventDefault();
        overlay.classList.remove("hidden-overlay");
        overlay.classList.add("visible-overlay");
        // Load markdown content when opening
        loadMarkdownContent();
        // trap focus on dialog
        dialog.focus();
      }

      function closeInstructions() {
        overlay.classList.remove("visible-overlay");
        overlay.classList.add("hidden-overlay");
        // Return focus to the opener button
        openBtn.focus();
      }

      openBtn.addEventListener("click", openInstructions);
      closeBtn.addEventListener("click", closeInstructions);

      // Close on pressing Esc
      document.addEventListener("keydown", function(e) {
        if (e.key === "Escape" && overlay.classList.contains("visible-overlay")) {
          closeInstructions();
        }
      });

      // Close when clicking outside the dialog
      overlay.addEventListener("click", function(e) {
        if (e.target === overlay) {
          closeInstructions();
        }
      });

      // Trap focus inside the dialog when open
      dialog.addEventListener("keydown", function(e) {
        if (e.key === "Tab") {
          const focusableElements = dialog.querySelectorAll("button, [href], input, select, textarea, [tabindex]:not([tabindex='-1'])");
          const firstElement = focusableElements[0];
          const lastElement = focusableElements[focusableElements.length - 1];

          if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      });
    })();
  </script>
</body>
</html>